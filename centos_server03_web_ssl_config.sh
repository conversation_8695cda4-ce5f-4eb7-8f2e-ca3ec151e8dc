#!/bin/bash
# CentOS Server03 - WEB服务器2配置脚本
# 系统: CentOS 9
# 功能: Apache + SSL + 虚拟目录，端口80,443

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
SERVER_IP="***************"
SUBNET_MASK="24"
DOMAIN="mufeng.yuanchu"

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# 检查是否以root用户运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户运行此脚本"
        exit 1
    fi
}

# 设置固定的服务IP配置
show_config() {
    print_header "CentOS WEB服务器2固定IP配置："
    print_info "WEB服务器2 IP: $SERVER_IP/$SUBNET_MASK"
    print_info "域名: www.$DOMAIN"
    echo ""

    read -p "确认使用以上固定配置? (y/n): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        print_error "用户取消配置"
        exit 1
    fi
}

# 添加服务IP地址
setup_ip() {
    print_header "配置网络IP地址..."

    # 获取网络接口名称
    INTERFACE=$(ip -o -4 route show to default | awk '{print $5}')

    if [ -z "$INTERFACE" ]; then
        print_error "无法确定网络接口"
        exit 1
    fi

    print_info "在网络接口 $INTERFACE 上添加服务IP地址..."

    # 检查IP是否已经存在
    if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
        print_warning "IP地址 $SERVER_IP 已经存在于接口 $INTERFACE 上"
        return 0
    fi

    # 添加IP地址到现有接口（不删除原有配置）
    if ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE 2>/dev/null; then
        print_success "成功添加IP地址 $SERVER_IP 到接口 $INTERFACE"
    else
        # 如果添加失败，可能是因为IP已存在，检查是否真的存在
        if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
            print_warning "IP地址 $SERVER_IP 已经存在，跳过添加"
        else
            print_error "添加IP地址失败"
            exit 1
        fi
    fi

    # 创建持久化配置（CentOS方式）
    print_info "创建持久化IP配置..."

    # 创建网络脚本来在启动时添加IP
    cat > /etc/systemd/system/web2-service-ip.service << EOF
[Unit]
Description=Add Web2 Service IP Address
After=network.target

[Service]
Type=oneshot
ExecStart=/bin/ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE
RemainAfterExit=yes
StandardOutput=journal

[Install]
WantedBy=multi-user.target
EOF

    # 启用服务
    systemctl enable web2-service-ip.service

    # 验证IP配置
    sleep 2
    print_info "当前接口 $INTERFACE 的IP配置："
    ip addr show $INTERFACE | grep inet

    print_success "服务IP地址添加完成，已配置开机自启"
}

# 配置CentOS软件源
configure_centos_repos() {
    print_header "配置CentOS软件源..."

    # 备份原始repo文件
    if [ ! -d "/etc/yum.repos.d.backup" ]; then
        cp -r /etc/yum.repos.d /etc/yum.repos.d.backup 2>/dev/null || true
        print_info "已备份原始软件源配置"
    fi

    # 配置EPEL仓库
    print_info "配置EPEL仓库..."
    if ! dnf list installed epel-release >/dev/null 2>&1; then
        dnf install -y epel-release || print_warning "EPEL仓库安装失败"
    fi

    # 清理和重建缓存
    dnf clean all
    dnf makecache

    print_success "CentOS软件源配置完成"
}

# 安装Apache和SSL
install_httpd_ssl() {
    print_header "安装Apache和SSL..."

    # 检查网络连接
    if ! ping -c 1 ******* >/dev/null 2>&1; then
        print_warning "网络连接异常，请检查网络设置"
    fi

    # 更新系统
    dnf update -y

    # 安装Apache和SSL (CentOS方式)
    if ! dnf install -y httpd mod_ssl openssl; then
        print_error "Apache和SSL安装失败，请检查网络连接或软件源配置"
        exit 1
    fi

    # 检查Apache是否安装成功
    if ! command -v httpd >/dev/null 2>&1; then
        print_error "Apache安装失败"
        exit 1
    fi

    print_success "Apache和SSL安装完成"
}

# 创建网站目录和内容
create_website() {
    print_header "创建网站目录和内容..."

    # 创建网站目录
    mkdir -p /var/www/yuanchu/xuni
    mkdir -p /var/www/yuanchu/company
    mkdir -p /var/www/yuanchu/products
    mkdir -p /var/www/yuanchu/contact

    # 创建主页
    cat > /var/www/yuanchu/yuanchu.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Mufeng's CentOS Website</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .info {
            padding: 30px;
        }
        .info h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }
        .info-card h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
        }
        .info-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .info-list li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .info-list li:last-child {
            border-bottom: none;
        }
        .links {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        .links a {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            margin: 5px 10px 5px 0;
            transition: all 0.3s ease;
        }
        .links a:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to Mufeng's CentOS Website</h1>
            <p>Server03 - CentOS Web服务器2</p>
        </div>

        <div class="info">
            <div class="info-grid">
                <div class="info-card">
                    <h3>服务器信息</h3>
                    <ul class="info-list">
                        <li><strong>操作系统:</strong> CentOS 9</li>
                        <li><strong>IP地址:</strong> ***************</li>
                        <li><strong>域名:</strong> www.mufeng.yuanchu</li>
                        <li><strong>服务:</strong> Apache + SSL</li>
                        <li><strong>端口:</strong> 80, 443</li>
                        <li><strong>SSL证书:</strong> 自签名证书</li>
                        <li><strong>状态:</strong> 正常运行</li>
                    </ul>
                </div>

                <div class="info-card">
                    <h3>技术特性</h3>
                    <ul class="info-list">
                        <li><strong>HTTP重定向:</strong> 自动跳转HTTPS</li>
                        <li><strong>虚拟目录:</strong> 支持</li>
                        <li><strong>SSL加密:</strong> 启用</li>
                        <li><strong>字符编码:</strong> UTF-8</li>
                        <li><strong>响应式设计:</strong> 支持</li>
                        <li><strong>SELinux:</strong> 兼容</li>
                    </ul>
                </div>
            </div>

            <div class="links">
                <h2>访问链接</h2>
                <a href="/">主页 (HTTPS)</a>
                <a href="/xuni/">虚拟目录 (HTTPS)</a>
                <a href="/company/">公司介绍</a>
                <a href="/products/">产品展示</a>
                <a href="/contact/">联系我们</a>
            </div>
        </div>
    </div>
</body>
</html>
EOF

    # 创建虚拟目录页面
    cat > /var/www/yuanchu/xuni/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mufeng Information - CentOS Virtual Directory</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.2em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 30px;
        }
        .content h2 {
            color: #2d3436;
            border-bottom: 2px solid #00b894;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .info-box {
            background: #f1f2f6;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #00b894;
        }
        .path-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }
        .path-info strong {
            color: #0984e3;
        }
        .nav-button {
            display: inline-block;
            background: #00b894;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        .nav-button:hover {
            background: #00a085;
            transform: translateY(-2px);
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #ddd;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li:before {
            content: "✓";
            color: #00b894;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Mufeng Information</h1>
            <p>CentOS 虚拟目录 - /xuni</p>
        </div>

        <div class="content">
            <h2>虚拟目录信息</h2>

            <div class="info-box">
                <p>这是一个CentOS Apache虚拟目录示例，展示了如何在CentOS系统上配置虚拟目录访问。虚拟目录允许将不同的物理路径映射到Web服务器的URL路径中。</p>
            </div>

            <h2>路径配置</h2>
            <div class="path-info">
                <strong>物理路径:</strong> /var/www/yuanchu/xuni<br>
                <strong>虚拟路径:</strong> /xuni<br>
                <strong>访问方式:</strong> https://www.mufeng.yuanchu/xuni/
            </div>

            <h2>技术特性</h2>
            <ul class="feature-list">
                <li>CentOS Apache Alias指令配置</li>
                <li>独立的目录权限控制</li>
                <li>支持HTTPS安全访问</li>
                <li>UTF-8字符编码支持</li>
                <li>响应式页面设计</li>
                <li>SELinux安全上下文配置</li>
            </ul>

            <a href="../yuanchu.html" class="nav-button">返回主页</a>
        </div>
    </div>
</body>
</html>
EOF

    # 创建公司介绍页面
    cat > /var/www/yuanchu/company/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公司介绍 - Mufeng Technology</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); overflow: hidden; }
        .header { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .nav-button { display: inline-block; background: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 25px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Mufeng Technology</h1>
            <p>专业的CentOS解决方案提供商</p>
        </div>
        <div class="content">
            <h2>关于我们</h2>
            <p>Mufeng Technology是一家专注于CentOS系统集成和网络解决方案的技术公司。我们提供专业的DNS服务、Web服务器配置和SSL安全解决方案。</p>
            <h2>我们的服务</h2>
            <ul>
                <li>CentOS系统部署与维护</li>
                <li>BIND DNS服务器配置</li>
                <li>Apache Web服务器优化</li>
                <li>SSL证书管理与配置</li>
                <li>网络安全解决方案</li>
            </ul>
            <a href="../yuanchu.html" class="nav-button">返回主页</a>
        </div>
    </div>
</body>
</html>
EOF

    # 创建产品展示页面
    cat > /var/www/yuanchu/products/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品展示 - Mufeng Technology</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); overflow: hidden; }
        .header { background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .product { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 10px; border-left: 4px solid #e74c3c; }
        .nav-button { display: inline-block; background: #e74c3c; color: white; padding: 12px 24px; text-decoration: none; border-radius: 25px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>产品展示</h1>
            <p>我们的技术产品与解决方案</p>
        </div>
        <div class="content">
            <div class="product">
                <h3>CentOS DNS服务器</h3>
                <p>基于BIND的高性能DNS解决方案，支持域名解析、负载均衡和故障转移。</p>
            </div>
            <div class="product">
                <h3>Apache Web服务器</h3>
                <p>优化配置的Apache服务器，支持PHP、SSL和虚拟主机配置。</p>
            </div>
            <div class="product">
                <h3>SSL安全解决方案</h3>
                <p>完整的SSL证书管理和HTTPS配置，确保网站安全访问。</p>
            </div>
            <a href="../yuanchu.html" class="nav-button">返回主页</a>
        </div>
    </div>
</body>
</html>
EOF

    # 创建联系我们页面
    cat > /var/www/yuanchu/contact/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系我们 - Mufeng Technology</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); overflow: hidden; }
        .header { background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%); color: white; padding: 30px; text-align: center; }
        .content { padding: 30px; }
        .contact-info { background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 10px; border-left: 4px solid #27ae60; }
        .nav-button { display: inline-block; background: #27ae60; color: white; padding: 12px 24px; text-decoration: none; border-radius: 25px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>联系我们</h1>
            <p>获取专业的技术支持</p>
        </div>
        <div class="content">
            <div class="contact-info">
                <h3>技术支持</h3>
                <p><strong>服务器地址:</strong> ***************</p>
                <p><strong>域名:</strong> www.mufeng.yuanchu</p>
                <p><strong>邮箱:</strong> <EMAIL></p>
                <p><strong>技术热线:</strong> 400-123-4567</p>
            </div>
            <div class="contact-info">
                <h3>服务时间</h3>
                <p><strong>工作日:</strong> 9:00 - 18:00</p>
                <p><strong>周末:</strong> 10:00 - 16:00</p>
                <p><strong>紧急支持:</strong> 24/7 在线</p>
            </div>
            <a href="../yuanchu.html" class="nav-button">返回主页</a>
        </div>
    </div>
</body>
</html>
EOF

    # 设置权限
    chown -R apache:apache /var/www
    chmod -R 755 /var/www

    print_success "网站目录和内容创建完成"
}

# 生成SSL证书
generate_ssl_cert() {
    print_header "生成SSL证书..."

    # 创建SSL证书目录 (CentOS方式)
    mkdir -p /etc/pki/tls/certs
    mkdir -p /etc/pki/tls/private

    # 生成私钥
    openssl genrsa -out /etc/pki/tls/private/www.$DOMAIN.key 2048

    # 生成证书签名请求
    openssl req -new -key /etc/pki/tls/private/www.$DOMAIN.key -out /tmp/www.$DOMAIN.csr << EOF
CN
Beijing
Beijing
Mufeng Organization
IT Department
www.$DOMAIN
admin@$DOMAIN


EOF

    # 生成自签名证书
    openssl x509 -req -days 365 -in /tmp/www.$DOMAIN.csr -signkey /etc/pki/tls/private/www.$DOMAIN.key -out /etc/pki/tls/certs/www.$DOMAIN.crt

    # 设置权限
    chmod 600 /etc/pki/tls/private/www.$DOMAIN.key
    chmod 644 /etc/pki/tls/certs/www.$DOMAIN.crt

    # 清理临时文件
    rm -f /tmp/www.$DOMAIN.csr

    print_success "SSL证书生成完成"
}

# 配置Apache虚拟主机
configure_apache() {
    print_header "配置Apache虚拟主机..."

    # 创建HTTP虚拟主机配置 (重定向到HTTPS)
    cat > /etc/httpd/conf.d/www-redirect.conf << EOF
<VirtualHost *:80>
    ServerName www.$DOMAIN
    ServerAlias $SERVER_IP
    ServerAlias localhost

    # 强制重定向到HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # 备用重定向方法
    Redirect permanent / https://www.$DOMAIN/
</VirtualHost>
EOF

    # 创建HTTPS虚拟主机配置
    cat > /etc/httpd/conf.d/www-ssl.conf << EOF
<VirtualHost *:443>
    ServerName www.$DOMAIN
    ServerAlias $SERVER_IP
    ServerAlias localhost
    DocumentRoot /var/www/yuanchu
    DirectoryIndex yuanchu.html

    # SSL配置
    SSLEngine on
    SSLCertificateFile /etc/pki/tls/certs/www.$DOMAIN.crt
    SSLCertificateKeyFile /etc/pki/tls/private/www.$DOMAIN.key

    # 安全头部
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set X-Frame-Options DENY
    Header always set X-Content-Type-Options nosniff

    # 虚拟目录配置
    Alias /xuni /var/www/yuanchu/xuni
    Alias /company /var/www/yuanchu/company
    Alias /products /var/www/yuanchu/products
    Alias /contact /var/www/yuanchu/contact

    <Directory "/var/www/yuanchu/xuni">
        AllowOverride All
        Require all granted
        Options Indexes FollowSymLinks
        DirectoryIndex index.html
    </Directory>

    <Directory "/var/www/yuanchu/company">
        AllowOverride All
        Require all granted
        Options Indexes FollowSymLinks
        DirectoryIndex index.html
    </Directory>

    <Directory "/var/www/yuanchu/products">
        AllowOverride All
        Require all granted
        Options Indexes FollowSymLinks
        DirectoryIndex index.html
    </Directory>

    <Directory "/var/www/yuanchu/contact">
        AllowOverride All
        Require all granted
        Options Indexes FollowSymLinks
        DirectoryIndex index.html
    </Directory>

    <Directory "/var/www/yuanchu">
        AllowOverride All
        Require all granted
        Options Indexes FollowSymLinks
    </Directory>

    # 添加NAT网络访问支持 - 允许从任何IP访问
    # 这个配置已经在各个Directory指令中通过"Require all granted"实现

    ErrorLog /var/log/httpd/www-ssl-error.log
    CustomLog /var/log/httpd/www-ssl-access.log combined
</VirtualHost>
EOF

    print_success "Apache虚拟主机配置完成"
}

# 配置防火墙
configure_firewall() {
    print_header "配置CentOS防火墙..."

    # 检查firewalld状态
    if ! systemctl is-active --quiet firewalld; then
        systemctl enable firewalld
        systemctl start firewalld
        print_info "已启动firewalld服务"
    fi

    # 添加HTTP和HTTPS服务到防火墙
    firewall-cmd --permanent --add-service=http
    firewall-cmd --permanent --add-service=https
    firewall-cmd --permanent --add-port=80/tcp
    firewall-cmd --permanent --add-port=443/tcp

    # 重新加载防火墙配置
    firewall-cmd --reload

    print_success "防火墙配置完成"
}

# 配置SELinux
configure_selinux() {
    print_header "配置SELinux..."

    # 检查SELinux状态
    if command -v getenforce >/dev/null 2>&1; then
        SELINUX_STATUS=$(getenforce)
        print_info "当前SELinux状态: $SELINUX_STATUS"

        if [ "$SELINUX_STATUS" = "Enforcing" ]; then
            print_info "配置SELinux策略以支持Apache和SSL..."

            # 设置Apache相关的SELinux上下文
            setsebool -P httpd_can_network_connect on
            restorecon -R /var/www/
            restorecon -R /etc/pki/tls/

            print_success "SELinux配置完成"
        fi
    else
        print_info "SELinux未安装或不可用"
    fi
}

# 启动Apache
start_apache() {
    print_header "启动Apache服务..."

    # 启用SSL模块 (CentOS方式)
    print_info "启用Apache模块..."
    # CentOS的mod_ssl在安装时已自动启用

    # 启动服务
    systemctl enable httpd
    systemctl restart httpd

    # 检查服务状态
    if systemctl is-active --quiet httpd; then
        print_success "Apache服务启动成功"
    else
        print_error "Apache服务启动失败"
        journalctl -u httpd --no-pager -l
        exit 1
    fi
}

# 测试SSL配置
test_ssl() {
    print_header "测试SSL配置..."

    # 等待服务完全启动
    sleep 5

    # 测试HTTP重定向
    print_info "测试HTTP重定向..."
    HTTP_RESPONSE=$(curl -s -I http://localhost/ | head -n 1)
    if echo "$HTTP_RESPONSE" | grep -q "301\|302"; then
        print_success "HTTP重定向配置正常"
    else
        print_warning "HTTP重定向可能有问题"
    fi

    # 测试HTTPS访问
    print_info "测试HTTPS访问..."
    if curl -k -s https://localhost/yuanchu.html | grep -q "Welcome to Mufeng's CentOS Website"; then
        print_success "HTTPS主页访问正常"
    else
        print_warning "HTTPS主页访问异常"
    fi

    # 测试虚拟目录
    print_info "测试虚拟目录..."
    if curl -k -s https://localhost/xuni/ | grep -q "Mufeng Information"; then
        print_success "虚拟目录访问正常"
    else
        print_warning "虚拟目录访问异常"
    fi

    # 测试公司介绍页面
    print_info "测试公司介绍页面..."
    if curl -k -s https://localhost/company/ | grep -q "Mufeng Technology"; then
        print_success "公司介绍页面访问正常"
    else
        print_warning "公司介绍页面访问异常"
    fi

    # 测试产品展示页面
    print_info "测试产品展示页面..."
    if curl -k -s https://localhost/products/ | grep -q "产品展示"; then
        print_success "产品展示页面访问正常"
    else
        print_warning "产品展示页面访问异常"
    fi

    # 测试联系我们页面
    print_info "测试联系我们页面..."
    if curl -k -s https://localhost/contact/ | grep -q "联系我们"; then
        print_success "联系我们页面访问正常"
    else
        print_warning "联系我们页面访问异常"
    fi

    print_success "SSL测试完成"
}

# 添加本地hosts条目
add_hosts_entry() {
    print_header "添加本地hosts条目..."

    if ! grep -q "www.$DOMAIN" /etc/hosts; then
        echo "$SERVER_IP www.$DOMAIN" >> /etc/hosts
        print_success "已添加hosts条目"
    else
        print_info "hosts条目已存在"
    fi
}

# 主函数
main() {
    print_header "开始配置CentOS Server03 - WEB服务器2..."
    echo ""

    # 检查root权限
    check_root

    # 显示配置信息
    show_config

    # 询问是否添加服务IP地址
    read -p "是否为WEB服务添加IP地址$SERVER_IP/$SUBNET_MASK? (不会影响现有网络配置) (y/n): " answer
    if [[ "$answer" =~ ^[Yy]$ ]]; then
        setup_ip
        echo ""
    fi

    # 配置软件源和安装服务
    configure_centos_repos
    echo ""
    install_httpd_ssl
    echo ""
    create_website
    echo ""
    generate_ssl_cert
    echo ""
    configure_apache
    echo ""
    configure_firewall
    echo ""
    configure_selinux
    echo ""
    start_apache
    echo ""
    add_hosts_entry
    echo ""
    test_ssl

    # 执行最终测试
    print_header "执行最终测试..."
    sleep 3

    # 测试HTTP重定向
    if curl -s -I http://127.0.0.1/ | grep -q "301\|302"; then
        print_success "HTTP重定向测试成功"
    else
        print_warning "HTTP重定向测试失败"
    fi

    # 测试HTTPS访问
    if curl -k -s https://127.0.0.1/yuanchu.html | grep -q "Welcome to Mufeng's CentOS Website"; then
        print_success "HTTPS访问测试成功"
    else
        print_warning "HTTPS访问测试失败"
    fi

    # 测试虚拟目录
    if curl -k -s https://127.0.0.1/xuni/ | grep -q "Mufeng Information"; then
        print_success "虚拟目录测试成功"
    else
        print_warning "虚拟目录测试失败"
    fi

    # 测试其他页面
    if curl -k -s https://127.0.0.1/company/ | grep -q "Mufeng Technology"; then
        print_success "公司介绍页面测试成功"
    else
        print_warning "公司介绍页面测试失败"
    fi

    if curl -k -s https://127.0.0.1/products/ | grep -q "产品展示"; then
        print_success "产品展示页面测试成功"
    else
        print_warning "产品展示页面测试失败"
    fi

    if curl -k -s https://127.0.0.1/contact/ | grep -q "联系我们"; then
        print_success "联系我们页面测试成功"
    else
        print_warning "联系我们页面测试失败"
    fi

    echo ""
    print_success "CentOS Server03 - WEB服务器2配置完成!"
    print_info "服务器地址: $SERVER_IP"
    print_info "访问地址: https://www.$DOMAIN 或 https://$SERVER_IP"
    print_info "服务: Apache + SSL"
    print_info "端口: 80 (重定向), 443 (HTTPS)"
    print_info "测试页面:"
    print_info "  - 主页: https://$SERVER_IP/yuanchu.html"
    print_info "  - 虚拟目录: https://$SERVER_IP/xuni/"
    print_info "  - 公司介绍: https://$SERVER_IP/company/"
    print_info "  - 产品展示: https://$SERVER_IP/products/"
    print_info "  - 联系我们: https://$SERVER_IP/contact/"
    print_info "注意: 使用自签名证书，浏览器会显示安全警告，选择继续访问即可"
    print_info "请确保DNS服务器已正确配置www.$DOMAIN指向$SERVER_IP"
}

# 执行主函数
main "$@"
