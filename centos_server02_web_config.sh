#!/bin/bash
# CentOS Server02 - WEB服务器1配置脚本
# 系统: CentOS 9
# 功能: Apache + MariaDB + WordPress，端口80

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
SERVER_IP="***************"
SUBNET_MASK="24"
DOMAIN="mufeng.yuanchu"

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# 检查是否以root用户运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户运行此脚本"
        exit 1
    fi
}

# 设置固定的服务IP配置
show_config() {
    print_header "CentOS WEB服务器1固定IP配置："
    print_info "WEB服务器1 IP: $SERVER_IP/$SUBNET_MASK"
    print_info "域名: web.$DOMAIN"
    echo ""

    read -p "确认使用以上固定配置? (y/n): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        print_error "用户取消配置"
        exit 1
    fi
}

# 添加服务IP地址
setup_ip() {
    print_header "配置网络IP地址..."

    # 获取网络接口名称
    INTERFACE=$(ip -o -4 route show to default | awk '{print $5}')

    if [ -z "$INTERFACE" ]; then
        print_error "无法确定网络接口"
        exit 1
    fi

    print_info "在网络接口 $INTERFACE 上添加服务IP地址..."

    # 检查IP是否已经存在
    if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
        print_warning "IP地址 $SERVER_IP 已经存在于接口 $INTERFACE 上"
        return 0
    fi

    # 添加IP地址到现有接口（不删除原有配置）
    if ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE 2>/dev/null; then
        print_success "成功添加IP地址 $SERVER_IP 到接口 $INTERFACE"
    else
        # 如果添加失败，可能是因为IP已存在，检查是否真的存在
        if ip addr show $INTERFACE | grep -q "$SERVER_IP"; then
            print_warning "IP地址 $SERVER_IP 已经存在，跳过添加"
        else
            print_error "添加IP地址失败"
            exit 1
        fi
    fi

    # 创建持久化配置（CentOS方式）
    print_info "创建持久化IP配置..."

    # 创建网络脚本来在启动时添加IP
    cat > /etc/systemd/system/web1-service-ip.service << EOF
[Unit]
Description=Add Web1 Service IP Address
After=network.target

[Service]
Type=oneshot
ExecStart=/bin/ip addr add $SERVER_IP/$SUBNET_MASK dev $INTERFACE
RemainAfterExit=yes
StandardOutput=journal

[Install]
WantedBy=multi-user.target
EOF

    # 启用服务
    systemctl enable web1-service-ip.service

    # 验证IP配置
    sleep 2
    print_info "当前接口 $INTERFACE 的IP配置："
    ip addr show $INTERFACE | grep inet

    print_success "服务IP地址添加完成，已配置开机自启"
}

# 配置CentOS软件源
configure_centos_repos() {
    print_header "配置CentOS软件源..."

    # 备份原始repo文件
    if [ ! -d "/etc/yum.repos.d.backup" ]; then
        cp -r /etc/yum.repos.d /etc/yum.repos.d.backup 2>/dev/null || true
        print_info "已备份原始软件源配置"
    fi

    # 配置EPEL仓库
    print_info "配置EPEL仓库..."
    if ! dnf list installed epel-release >/dev/null 2>&1; then
        dnf install -y epel-release || print_warning "EPEL仓库安装失败"
    fi

    # 清理和重建缓存
    dnf clean all
    dnf makecache

    print_success "CentOS软件源配置完成"
}

# 安装Apache和PHP
install_apache_php() {
    print_header "安装Apache和PHP..."

    # 更新系统
    dnf update -y

    # 安装Apache和PHP (CentOS方式)
    if ! dnf install -y httpd php php-mysqlnd php-json php-gd php-mbstring php-xml php-zip php-curl; then
        print_error "Apache和PHP安装失败，请检查网络连接或软件源配置"
        exit 1
    fi

    print_success "Apache和PHP安装完成"
}

# 配置Apache
configure_apache() {
    print_header "配置Apache..."

    # 创建网站目录
    mkdir -p /var/www/website

    # 配置虚拟主机 (CentOS Apache)
    cat > /etc/httpd/conf.d/website.conf << EOF
<VirtualHost *:80>
    ServerName web.$DOMAIN
    ServerAlias $SERVER_IP
    ServerAlias localhost
    DocumentRoot /var/www/website
    DirectoryIndex index.html index.php

    <Directory /var/www/website>
        AllowOverride All
        Require all granted
        Options Indexes FollowSymLinks
    </Directory>

    # 添加MIME类型支持
    <FilesMatch "\.php$">
        SetHandler application/x-httpd-php
    </FilesMatch>

    # 添加NAT网络访问支持
    # 允许从任何IP访问，包括宿主机
    <RequireAll>
        Require all granted
    </RequireAll>

    ErrorLog /var/log/httpd/website-error.log
    CustomLog /var/log/httpd/website-access.log combined
</VirtualHost>
EOF

    # 创建主页面
    cat > /var/www/website/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to CentOS Web Server 1</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .info {
            padding: 30px;
        }
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #e74c3c;
            margin: 20px 0;
        }
        .links a {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            margin: 5px 10px 5px 0;
            transition: all 0.3s ease;
        }
        .links a:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to CentOS Web Server 1</h1>
            <p>Server02 - Apache + MariaDB + WordPress</p>
        </div>

        <div class="info">
            <div class="info-card">
                <h3>服务器信息</h3>
                <p><strong>操作系统:</strong> CentOS 9</p>
                <p><strong>IP地址:</strong> ***************</p>
                <p><strong>域名:</strong> web.mufeng.yuanchu</p>
                <p><strong>服务:</strong> Apache + MariaDB + WordPress</p>
                <p><strong>端口:</strong> 80</p>
                <p><strong>状态:</strong> 正常运行</p>
            </div>

            <div class="links">
                <h3>功能测试</h3>
                <a href="index.php">PHP信息页面</a>
                <a href="test.php">PHP测试</a>
            </div>
        </div>
    </div>
</body>
</html>
EOF

    # 创建PHP测试页面
    cat > /var/www/website/index.php << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CentOS Web Server 1 - PHP Info</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #e74c3c; color: white; padding: 20px; border-radius: 5px; text-align: center; margin-bottom: 20px; }
        .info { margin: 20px 0; }
        .back-link { display: inline-block; background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to CentOS Web Server 1</h1>
            <p>PHP信息页面</p>
        </div>
        <div class="info">
            <h2>服务器信息</h2>
            <p><strong>操作系统:</strong> CentOS 9</p>
            <p><strong>IP地址:</strong> ***************</p>
            <p><strong>域名:</strong> web.mufeng.yuanchu</p>
            <p><strong>PHP版本:</strong> <?php echo phpversion(); ?></p>
            <p><strong>当前时间:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            <p><strong>服务器软件:</strong> <?php echo $_SERVER['SERVER_SOFTWARE']; ?></p>
            <a href="index.html" class="back-link">返回主页</a>
        </div>
    </div>
</body>
</html>
EOF

    # 创建简单的PHP测试页面
    cat > /var/www/website/test.php << 'EOF'
<?php
echo "<h1>Welcome to CentOS Web Server 1</h1>";
echo "<p>Operating System: CentOS 9</p>";
echo "<p>Server IP: ***************</p>";
echo "<p>Domain: web.mufeng.yuanchu</p>";
echo "<p>Service: Apache + MariaDB + WordPress</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";
?>
EOF

    # 设置权限
    chown -R apache:apache /var/www/website
    chmod -R 755 /var/www/website

    print_success "Apache配置完成"
}

# 安装MariaDB
install_mariadb() {
    print_header "安装MariaDB..."

    # 安装MariaDB (CentOS方式)
    dnf install -y mariadb-server mariadb

    # 启动MariaDB
    systemctl enable mariadb
    systemctl start mariadb

    print_success "MariaDB安装完成"
}

# 配置MariaDB
configure_mariadb() {
    print_header "配置MariaDB..."

    # 设置MariaDB root密码
    read -s -p "请输入MariaDB root密码: " MYSQL_ROOT_PASSWORD
    echo ""

    # 运行安全配置脚本
    mysql_secure_installation << EOF

y
$MYSQL_ROOT_PASSWORD
$MYSQL_ROOT_PASSWORD
y
y
y
y
EOF

    # 创建用户user1
    mysql -u root -p$MYSQL_ROOT_PASSWORD << EOF
CREATE USER 'user1'@'localhost' IDENTIFIED BY '123456';
GRANT ALL PRIVILEGES ON *.* TO 'user1'@'localhost';
FLUSH PRIVILEGES;
EXIT;
EOF

    print_success "MariaDB配置完成，用户user1已创建，密码为123456"
}

# 安装WordPress
install_wordpress() {
    print_header "安装WordPress..."

    # 安装wget
    dnf install -y wget

    # 下载WordPress
    cd /tmp
    wget https://wordpress.org/latest.tar.gz

    # 解压WordPress
    tar -xzvf latest.tar.gz -C /var/www/website/
    mv /var/www/website/wordpress/* /var/www/website/
    rm -rf /var/www/website/wordpress

    # 创建WordPress数据库
    read -s -p "请输入MariaDB root密码: " MYSQL_ROOT_PASSWORD
    echo ""

    mysql -u root -p$MYSQL_ROOT_PASSWORD << EOF
CREATE DATABASE wordpress;
GRANT ALL PRIVILEGES ON wordpress.* TO 'user1'@'localhost';
FLUSH PRIVILEGES;
EXIT;
EOF

    # 配置WordPress
    cp /var/www/website/wp-config-sample.php /var/www/website/wp-config.php

    # 修改数据库配置
    sed -i "s/database_name_here/wordpress/" /var/www/website/wp-config.php
    sed -i "s/username_here/user1/" /var/www/website/wp-config.php
    sed -i "s/password_here/123456/" /var/www/website/wp-config.php

    # 生成安全密钥
    KEYS=$(curl -s https://api.wordpress.org/secret-key/1.1/salt/)
    KEYS=$(echo "$KEYS" | sed "s/'/\\\'/g")
    sed -i "/define( 'AUTH_KEY'/,/define( 'NONCE_SALT'/d" /var/www/website/wp-config.php
    sed -i "/put your unique phrase here/a\\$KEYS" /var/www/website/wp-config.php

    # 设置权限 (CentOS方式)
    chown -R apache:apache /var/www/website
    chmod -R 755 /var/www/website

    print_success "WordPress安装完成"
}

# 配置防火墙
configure_firewall() {
    print_header "配置CentOS防火墙..."

    # 检查firewalld状态
    if ! systemctl is-active --quiet firewalld; then
        systemctl enable firewalld
        systemctl start firewalld
        print_info "已启动firewalld服务"
    fi

    # 添加HTTP服务到防火墙
    firewall-cmd --permanent --add-service=http
    firewall-cmd --permanent --add-port=80/tcp

    # 重新加载防火墙配置
    firewall-cmd --reload

    print_success "防火墙配置完成"
}

# 配置SELinux
configure_selinux() {
    print_header "配置SELinux..."

    # 检查SELinux状态
    if command -v getenforce >/dev/null 2>&1; then
        SELINUX_STATUS=$(getenforce)
        print_info "当前SELinux状态: $SELINUX_STATUS"

        if [ "$SELINUX_STATUS" = "Enforcing" ]; then
            print_info "配置SELinux策略以支持Apache和PHP..."

            # 设置Apache相关的SELinux上下文
            setsebool -P httpd_can_network_connect on
            setsebool -P httpd_can_network_connect_db on
            restorecon -R /var/www/website/

            print_success "SELinux配置完成"
        fi
    else
        print_info "SELinux未安装或不可用"
    fi
}

# 启动Apache
start_apache() {
    print_header "启动Apache服务..."

    # 启动服务 (CentOS方式)
    systemctl enable httpd
    systemctl restart httpd

    # 检查服务状态
    if systemctl is-active --quiet httpd; then
        print_success "Apache服务启动成功"
    else
        print_error "Apache服务启动失败"
        journalctl -u httpd --no-pager -l
        exit 1
    fi
}

# 测试Web服务
test_web() {
    print_header "测试Web服务..."

    # 测试Apache
    if curl -s http://localhost/index.php | grep -q "Welcome to CentOS Web Server 1"; then
        print_success "Web服务器测试成功"
    else
        print_warning "Web服务器测试失败"
    fi

    print_success "Web服务测试完成"
    print_info "请在浏览器中访问 http://web.$DOMAIN 完成WordPress安装"
}

# 添加本地hosts条目
add_hosts_entry() {
    print_header "添加本地hosts条目..."

    if ! grep -q "web.$DOMAIN" /etc/hosts; then
        echo "$SERVER_IP web.$DOMAIN" >> /etc/hosts
        print_success "已添加hosts条目"
    else
        print_info "hosts条目已存在"
    fi
}

# 主函数
main() {
    print_header "开始配置CentOS Server02 - WEB服务器1..."
    echo ""

    # 检查root权限
    check_root

    # 显示配置信息
    show_config

    # 询问是否添加服务IP地址
    read -p "是否为WEB服务添加IP地址$SERVER_IP/$SUBNET_MASK? (不会影响现有网络配置) (y/n): " answer
    if [[ "$answer" =~ ^[Yy]$ ]]; then
        setup_ip
        echo ""
    fi

    # 配置软件源和安装服务
    configure_centos_repos
    echo ""
    install_apache_php
    echo ""
    configure_apache
    echo ""
    install_mariadb
    echo ""
    configure_mariadb
    echo ""
    install_wordpress
    echo ""
    configure_firewall
    echo ""
    configure_selinux
    echo ""
    start_apache
    echo ""
    add_hosts_entry
    echo ""
    test_web

    # 测试Web服务
    print_header "执行最终测试..."
    sleep 3

    # 测试HTTP访问
    if curl -s http://127.0.0.1/ | grep -q "Welcome to CentOS Web Server 1"; then
        print_success "HTTP访问测试成功"
    else
        print_warning "HTTP访问测试失败"
    fi

    # 测试PHP
    if curl -s http://127.0.0.1/test.php | grep -q "Welcome to CentOS Web Server 1"; then
        print_success "PHP测试成功"
    else
        print_warning "PHP测试失败"
    fi

    echo ""
    print_success "CentOS Server02 - WEB服务器1配置完成!"
    print_info "服务器地址: $SERVER_IP"
    print_info "访问地址: http://web.$DOMAIN 或 http://$SERVER_IP"
    print_info "服务: Apache + MariaDB + WordPress"
    print_info "端口: 80"
    print_info "测试页面:"
    print_info "  - 主页: http://$SERVER_IP/"
    print_info "  - PHP信息: http://$SERVER_IP/index.php"
    print_info "  - PHP测试: http://$SERVER_IP/test.php"
    print_info "请确保DNS服务器已正确配置web.$DOMAIN指向$SERVER_IP"
}

# 执行主函数
main "$@"
