#!/bin/bash
# CentOS客户端DNS配置脚本
# 专门为CentOS系统设计的客户端配置

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器配置
DNS_SERVER="192.168.182.128"
WEB1_SERVER="192.168.182.130"
WEB2_SERVER="192.168.182.131"
DOMAIN="mufeng.yuanchu"

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# 检查是否以root用户运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户运行此脚本"
        exit 1
    fi
}

# 检测CentOS版本
detect_centos_version() {
    print_header "检测CentOS版本..."
    
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        print_info "操作系统: $NAME"
        print_info "版本: $VERSION"
        
        if [[ "$NAME" == *"CentOS"* ]]; then
            CENTOS_VERSION=$(echo $VERSION_ID | cut -d. -f1)
            print_success "检测到CentOS $CENTOS_VERSION"
        else
            print_warning "非CentOS系统，可能需要调整配置方法"
        fi
    else
        print_warning "无法检测系统版本"
    fi
}

# 配置NetworkManager DNS
configure_networkmanager_dns() {
    print_header "使用NetworkManager配置DNS..."
    
    # 检查NetworkManager是否运行
    if ! systemctl is-active --quiet NetworkManager; then
        print_warning "NetworkManager未运行，尝试启动..."
        systemctl start NetworkManager
        systemctl enable NetworkManager
    fi
    
    # 获取活动连接
    local connection=$(nmcli -t -f NAME connection show --active | head -n1)
    
    if [ -z "$connection" ]; then
        print_error "未找到活动的网络连接"
        return 1
    fi
    
    print_info "配置网络连接: $connection"
    
    # 配置DNS服务器
    nmcli connection modify "$connection" ipv4.dns "$DNS_SERVER,*******"
    nmcli connection modify "$connection" ipv4.dns-search "$DOMAIN"
    nmcli connection modify "$connection" ipv4.ignore-auto-dns yes
    
    # 重新激活连接
    nmcli connection down "$connection"
    nmcli connection up "$connection"
    
    print_success "NetworkManager DNS配置完成"
}

# 配置传统resolv.conf
configure_resolv_conf() {
    print_header "配置传统resolv.conf..."
    
    # 备份原始文件
    if [ ! -f /etc/resolv.conf.backup ]; then
        cp /etc/resolv.conf /etc/resolv.conf.backup
        print_info "已备份原始resolv.conf"
    fi
    
    # 创建新的resolv.conf
    cat > /etc/resolv.conf << EOF
# CentOS客户端DNS配置
nameserver $DNS_SERVER
nameserver *******
search $DOMAIN
EOF
    
    # 防止NetworkManager覆盖
    chattr +i /etc/resolv.conf 2>/dev/null || true
    
    print_success "resolv.conf配置完成"
}

# 配置systemd-resolved (如果可用)
configure_systemd_resolved() {
    print_header "配置systemd-resolved..."
    
    if systemctl is-enabled systemd-resolved >/dev/null 2>&1; then
        print_info "检测到systemd-resolved服务"
        
        # 备份配置文件
        if [ ! -f /etc/systemd/resolved.conf.backup ]; then
            cp /etc/systemd/resolved.conf /etc/systemd/resolved.conf.backup 2>/dev/null || true
        fi
        
        # 配置systemd-resolved
        cat > /etc/systemd/resolved.conf << EOF
[Resolve]
DNS=$DNS_SERVER *******
Domains=$DOMAIN
EOF
        
        # 重启服务
        systemctl restart systemd-resolved
        
        print_success "systemd-resolved配置完成"
    else
        print_info "systemd-resolved服务不可用，跳过配置"
    fi
}

# 配置hosts文件
configure_hosts() {
    print_header "配置hosts文件..."
    
    # 备份hosts文件
    if [ ! -f /etc/hosts.backup ]; then
        cp /etc/hosts /etc/hosts.backup
        print_info "已备份原始hosts文件"
    fi
    
    # 检查是否已存在配置
    if grep -q "$DOMAIN" /etc/hosts; then
        print_warning "hosts文件中已存在$DOMAIN相关配置"
        read -p "是否覆盖现有配置? (y/n): " overwrite
        if [[ "$overwrite" =~ ^[Yy]$ ]]; then
            # 移除现有配置
            sed -i "/$DOMAIN/d" /etc/hosts
        else
            print_info "跳过hosts文件配置"
            return 0
        fi
    fi
    
    # 添加域名解析
    cat >> /etc/hosts << EOF

# Mufeng.yuanchu 服务器配置 (CentOS)
$DNS_SERVER dns.$DOMAIN
$WEB1_SERVER web.$DOMAIN
$WEB2_SERVER www.$DOMAIN
EOF
    
    print_success "hosts文件配置完成"
}

# 配置防火墙
configure_firewall() {
    print_header "配置CentOS防火墙..."
    
    # 检查firewalld状态
    if systemctl is-active --quiet firewalld; then
        print_info "配置firewalld规则..."
        
        # 允许DNS查询
        firewall-cmd --permanent --add-service=dns 2>/dev/null || true
        
        # 允许HTTP和HTTPS访问
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        
        # 重新加载防火墙
        firewall-cmd --reload
        
        print_success "防火墙配置完成"
    else
        print_info "firewalld未运行，跳过防火墙配置"
    fi
}

# 测试网络连通性
test_network_connectivity() {
    print_header "测试网络连通性..."
    
    local servers=("$DNS_SERVER:DNS服务器" "$WEB1_SERVER:WEB服务器1" "$WEB2_SERVER:WEB服务器2")
    
    for server_info in "${servers[@]}"; do
        local ip=$(echo $server_info | cut -d: -f1)
        local name=$(echo $server_info | cut -d: -f2)
        
        if ping -c 3 -W 3 $ip >/dev/null 2>&1; then
            print_success "$name ($ip) 连通正常"
        else
            print_error "$name ($ip) 连通失败"
        fi
    done
}

# 测试DNS解析
test_dns_resolution() {
    print_header "测试DNS解析..."
    
    local domains=("dns.$DOMAIN" "web.$DOMAIN" "www.$DOMAIN")
    
    for domain in "${domains[@]}"; do
        if command -v dig >/dev/null 2>&1; then
            local result=$(dig +short $domain 2>/dev/null)
            if [ ! -z "$result" ]; then
                print_success "$domain 解析成功 -> $result"
            else
                print_error "$domain 解析失败"
            fi
        elif command -v nslookup >/dev/null 2>&1; then
            if nslookup $domain >/dev/null 2>&1; then
                print_success "$domain nslookup解析成功"
            else
                print_error "$domain nslookup解析失败"
            fi
        else
            print_warning "DNS查询工具不可用"
            break
        fi
    done
}

# 测试HTTP服务
test_http_services() {
    print_header "测试HTTP服务..."
    
    if command -v curl >/dev/null 2>&1; then
        # 测试WEB服务器1
        local http_code=$(curl -s -o /dev/null -w "%{http_code}" http://$WEB1_SERVER 2>/dev/null)
        if [ "$http_code" = "200" ]; then
            print_success "WEB服务器1 HTTP服务正常 (状态码: $http_code)"
        else
            print_warning "WEB服务器1 HTTP服务异常 (状态码: $http_code)"
        fi
        
        # 测试WEB服务器2 HTTPS
        local https_code=$(curl -k -s -o /dev/null -w "%{http_code}" https://$WEB2_SERVER 2>/dev/null)
        if [ "$https_code" = "200" ]; then
            print_success "WEB服务器2 HTTPS服务正常 (状态码: $https_code)"
        else
            print_warning "WEB服务器2 HTTPS服务异常 (状态码: $https_code)"
        fi
        
        # 测试域名访问
        if curl -s http://web.$DOMAIN 2>/dev/null | grep -q "Welcome to CentOS Web Server 1"; then
            print_success "域名访问测试成功"
        else
            print_warning "域名访问测试失败"
        fi
    else
        print_warning "curl工具不可用，跳过HTTP服务测试"
    fi
}

# 安装必要工具
install_tools() {
    print_header "安装必要工具..."
    
    local tools=("bind-utils" "curl" "nmap-ncat")
    
    for tool in "${tools[@]}"; do
        if ! dnf list installed $tool >/dev/null 2>&1; then
            print_info "安装 $tool..."
            dnf install -y $tool
        else
            print_success "$tool 已安装"
        fi
    done
}

# 显示访问信息
show_access_info() {
    print_header "CentOS客户端访问信息"
    echo ""
    print_info "🔍 DNS服务器:"
    echo "   - 地址: $DNS_SERVER"
    echo "   - 端口: 53"
    echo ""
    print_info "🌐 WEB服务器1:"
    echo "   - HTTP访问: http://web.$DOMAIN"
    echo "   - IP访问: http://$WEB1_SERVER"
    echo "   - 服务: Apache + MariaDB + WordPress"
    echo ""
    print_info "🔒 WEB服务器2:"
    echo "   - HTTPS访问: https://www.$DOMAIN"
    echo "   - IP访问: https://$WEB2_SERVER"
    echo "   - 虚拟目录: https://www.$DOMAIN/xuni/"
    echo "   - 服务: Apache + SSL"
    echo ""
    print_info "📝 配置文件位置:"
    echo "   - DNS配置: /etc/resolv.conf"
    echo "   - hosts文件: /etc/hosts"
    echo "   - NetworkManager: nmcli connection show"
    echo ""
}

# 生成配置报告
generate_config_report() {
    print_header "生成配置报告..."
    
    local report_file="centos_client_config_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > $report_file << EOF
CentOS客户端DNS配置报告
生成时间: $(date)
系统信息: $(uname -a)

服务器配置:
- DNS服务器: $DNS_SERVER
- WEB服务器1: $WEB1_SERVER
- WEB服务器2: $WEB2_SERVER
- 域名: $DOMAIN

配置方法:
- NetworkManager DNS配置
- 传统resolv.conf配置
- hosts文件备用配置
- firewalld防火墙配置

访问地址:
- WEB服务器1: http://web.$DOMAIN
- WEB服务器2: https://www.$DOMAIN
- 虚拟目录: https://www.$DOMAIN/xuni/

CentOS特定配置:
- 使用dnf包管理器
- firewalld防火墙管理
- NetworkManager网络管理
- SELinux兼容配置

详细结果请查看控制台输出。
EOF
    
    print_success "配置报告已生成: $report_file"
}

# 主菜单
show_main_menu() {
    print_header "CentOS客户端DNS配置工具"
    echo ""
    echo "请选择要执行的操作:"
    echo "1) 使用NetworkManager配置DNS"
    echo "2) 使用传统resolv.conf配置DNS"
    echo "3) 配置hosts文件"
    echo "4) 测试网络连通性"
    echo "5) 测试DNS解析"
    echo "6) 测试HTTP服务"
    echo "7) 显示访问信息"
    echo "8) 生成配置报告"
    echo "9) 全部配置和测试"
    echo "0) 退出"
    echo ""
    
    read -p "请输入选项 (0-9): " choice
    
    case $choice in
        1) configure_networkmanager_dns ;;
        2) configure_resolv_conf ;;
        3) configure_hosts ;;
        4) test_network_connectivity ;;
        5) test_dns_resolution ;;
        6) test_http_services ;;
        7) show_access_info ;;
        8) generate_config_report ;;
        9) 
            install_tools
            echo ""
            configure_networkmanager_dns
            echo ""
            configure_hosts
            echo ""
            configure_firewall
            echo ""
            print_info "等待DNS配置生效..."
            sleep 3
            test_network_connectivity
            echo ""
            test_dns_resolution
            echo ""
            test_http_services
            echo ""
            show_access_info
            generate_config_report
            print_success "CentOS客户端配置完成!"
            ;;
        0) 
            print_info "退出配置工具"
            exit 0
            ;;
        *) 
            print_error "无效选项，请重新选择"
            ;;
    esac
    
    echo ""
    read -p "按回车键继续..." 
    show_main_menu
}

# 主函数
main() {
    print_header "CentOS客户端DNS配置工具"
    print_info "专门为CentOS系统设计的客户端配置"
    echo ""
    
    # 检查root权限
    check_root
    
    # 检测CentOS版本
    detect_centos_version
    echo ""
    
    # 显示主菜单
    show_main_menu
}

# 执行主函数
main "$@"
